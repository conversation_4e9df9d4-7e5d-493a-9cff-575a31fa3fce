"use client";

import { useMutation } from "@tanstack/react-query";
import { useAuth } from "./use-auth";
import { getAccessToken } from "@/lib";
import { $http } from "@/lib/api/http";
import { createContext, useContext, useState } from 'react';
import { CatalogResponse } from "@/constants/inspectoral-filter";
import { SearchResult } from "@/components/deals-finder/inspectora-result";

interface InspectoraDetailsRequest {
  asin: string;
  country: string;
  fullResponse?: boolean;
}

interface InspectoraDetailsContextType {
  mutate: (asins: string[]) => void;
  isPending: boolean;
  error: any;
  data: any;
  results: SearchResult[];
  setResults: React.Dispatch<React.SetStateAction<SearchResult[]>>;
}

const InspectoraDetailsContext = createContext<InspectoraDetailsContextType | undefined>(undefined);

export const useInspectoraDetailsContext = () => {
  const context = useContext(InspectoraDetailsContext);
  if (!context) {
    throw new Error('useInspectoraDetailsContext must be used within an InspectoraDetailsProvider');
  }
  return context;
};

export const InspectoraDetailsProvider = ({ children }: { children: React.ReactNode }) => {
  const [results, setResults] = useState<SearchResult[]>([]);
  const accessToken = typeof window !== 'undefined' ? getAccessToken() : null;

  const mutation = useMutation({
    mutationFn: async (asins: string[]) => {
      try {
        if (asins.length === 0) {
          throw new Error("No ASIN provided");
        }

        const response = await $http.post(
          `/amazon/api/v1/amz-product/catalog-item`,
          {
            asins,
          },
          {
            headers: {
              accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        return response.data;
      } catch (error) {
        console.error(`❌ Error fetching Inspectora details:`, error);
        throw error;
      }
    },
    onSuccess: (data) => {
        const newResults = data.data.successful_results
        console.log('hello here', newResults)
        const updatedResults = [...results, ...newResults];
        setResults(updatedResults);
    },
    gcTime: 10 * 60 * 1000,
  });

  const { mutate, isPending, error, data } = mutation;

  return (
    <InspectoraDetailsContext.Provider 
      value={{
        mutate,
        isPending,
        error,
        data,
        results,
        setResults
      }}
    >
      {children}
    </InspectoraDetailsContext.Provider>
  );
};
