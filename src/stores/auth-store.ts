import { AuthState, User } from "@/types/auth";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { useShallow } from "zustand/react/shallow";

interface AuthStore extends AuthState {
	// Actions
	setUser: (user: User) => void;
	setToken: (token: string) => void;
	login: (user: User, token: string) => void;
	logout: () => void;

	// Loading states
	isLoggingIn: boolean;
	isLoggingOut: boolean;
	setLoggingIn: (loading: boolean) => void;
	setLoggingOut: (loading: boolean) => void;

	// Helper methods
	hasRole: (role: string) => boolean;
	hasScope: (scope: string) => boolean;
	isAdmin: () => boolean;
	isSeller: () => boolean;
	isCustomer: () => boolean;
	isVerified: () => boolean;
	isBusinessUser: () => boolean;
}

export const useAuthStore = create<AuthStore>()(
	devtools(
		persist(
			(set, get) => ({
				// Initial state
				user: null,
				token: null,
				isAuthenticated: false,
				isLoggingIn: false,
				isLoggingOut: false,

				// Actions
				setUser: (user: User) => {
					set({ user, isAuthenticated: true });
				},

				setToken: (token: string) => {
					set({ token });
				},

				login: (user: User, token: string) => {
					set({
						user,
						token,
						isAuthenticated: true,
						isLoggingIn: false,
					});
					console.log("🔄 Auth store updated - user logged in");
				},

				logout: () => {
					set({
						user: null,
						token: null,
						isAuthenticated: false,
						isLoggingOut: false,
					});
					console.log("🔄 Auth store updated - user logged out");
				},

				setLoggingIn: (loading: boolean) => {
					set({ isLoggingIn: loading });
				},

				setLoggingOut: (loading: boolean) => {
					set({ isLoggingOut: loading });
				},

				// Helper methods
				hasRole: (role: string) => {
					const { user } = get();
					return user?.role === role;
				},

				hasScope: (scope: string) => {
					const { user } = get();
					if (!user?.scopes) return false;

					// Admin has all permissions (check for wildcard)
					if (user.scopes.includes("*")) return true;

					return user.scopes.includes(scope);
				},

				isAdmin: () => {
					const { user } = get();
					return user?.role === "admin";
				},

				isSeller: () => {
					const { user } = get();
					return user?.role === "seller" || user?.role === "admin";
				},

				isCustomer: () => {
					const { user } = get();
					return user?.role === "customer";
				},

				isVerified: () => {
					const { user } = get();
					return user?.is_verified ?? false;
				},

				isBusinessUser: () => {
					const { user } = get();
					return !!(
						user?.business_name ||
						user?.role === "seller" ||
						user?.role === "admin"
					);
				},
			}),
			{
				name: "auth-store",
				// Only persist essential non-sensitive data
				partialize: (state) => ({
					user: state.user,
					isAuthenticated: state.isAuthenticated,
					// Don't persist tokens in Zustand - they're in localStorage
				}),
				// Handle migration and storage errors gracefully
				onRehydrateStorage: () => (state) => {
					if (!state) {
						console.warn(
							"Failed to rehydrate auth store, using defaults"
						);
						return;
					}
					console.log("Auth store rehydrated successfully");
				},
			}
		),
		{
			name: "auth-store",
		}
	)
);

// Selector hooks for different parts of the auth state
export const useAuth = () =>
	useAuthStore(
		useShallow((state) => ({
			user: state.user,
			token: state.token,
			isAuthenticated: state.isAuthenticated,
			isLoggingIn: state.isLoggingIn,
			isLoggingOut: state.isLoggingOut,
		}))
	);

export const useAuthActions = () =>
	useAuthStore(
		useShallow((state) => ({
			login: state.login,
			logout: state.logout,
			setLoggingIn: state.setLoggingIn,
			setLoggingOut: state.setLoggingOut,
			setUser: state.setUser,
			setToken: state.setToken,
		}))
	);

export const useAuthPermissions = () =>
	useAuthStore(
		useShallow((state) => ({
			hasRole: state.hasRole,
			hasScope: state.hasScope,
			isAdmin: state.isAdmin,
			isSeller: state.isSeller,
			isCustomer: state.isCustomer,
			isVerified: state.isVerified,
			isBusinessUser: state.isBusinessUser,
		}))
	);

export const useAuthStatus = () =>
	useAuthStore(
		useShallow((state) => ({
			isAuthenticated: state.isAuthenticated,
			isLoggingIn: state.isLoggingIn,
			isLoggingOut: state.isLoggingOut,
			isVerified: state.isVerified(),
			isAdmin: state.isAdmin(),
			isSeller: state.isSeller(),
			isCustomer: state.isCustomer(),
			isBusinessUser: state.isBusinessUser(),
		}))
	);

// Hook for getting user data safely
export const useUser = () => {
	const user = useAuthStore((state) => state.user);
	const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

	return {
		user: isAuthenticated ? user : null,
		isAuthenticated,
	};
};

// Hook for checking if user has specific permissions
export const usePermissions = (
	requiredRole?: string,
	requiredScope?: string
) => {
	return useAuthStore(
		useShallow((state) => {
			const hasRequiredRole = requiredRole
				? state.hasRole(requiredRole)
				: true;
			const hasRequiredScope = requiredScope
				? state.hasScope(requiredScope)
				: true;

			return {
				hasPermission: hasRequiredRole && hasRequiredScope,
				hasRole: state.hasRole,
				hasScope: state.hasScope,
				isAdmin: state.isAdmin(),
				isSeller: state.isSeller(),
				isCustomer: state.isCustomer(),
			};
		})
	);
};
